hoistPattern:
  - '*'
hoistedDependencies:
  '@esbuild/aix-ppc64@0.25.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.5':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.5':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.5':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.5':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.5':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.5':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.5':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.5':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.5':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.5':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.5':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.5':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.5':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.5':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.5':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.5':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.5':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.5':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.5':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.5':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.5':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.5':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.5':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.5':
    '@esbuild/win32-x64': private
  anymatch@3.1.3:
    anymatch: private
  balanced-match@1.0.2:
    balanced-match: private
  binary-extensions@2.3.0:
    binary-extensions: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  chokidar@3.6.0:
    chokidar: private
  concat-map@0.0.1:
    concat-map: private
  debug@4.4.1(supports-color@5.5.0):
    debug: private
  esbuild@0.25.5:
    esbuild: private
  fill-range@7.1.1:
    fill-range: private
  fsevents@2.3.3:
    fsevents: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  glob-parent@5.1.2:
    glob-parent: private
  has-flag@3.0.0:
    has-flag: private
  ignore-by-default@1.0.1:
    ignore-by-default: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-extglob@2.1.1:
    is-extglob: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  minimatch@3.1.2:
    minimatch: private
  ms@2.1.3:
    ms: private
  normalize-path@3.0.0:
    normalize-path: private
  picomatch@2.3.1:
    picomatch: private
  pstree.remy@1.1.8:
    pstree.remy: private
  readdirp@3.6.0:
    readdirp: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  semver@7.7.2:
    semver: private
  simple-update-notifier@2.0.0:
    simple-update-notifier: private
  supports-color@5.5.0:
    supports-color: private
  to-regex-range@5.0.1:
    to-regex-range: private
  touch@3.1.1:
    touch: private
  undefsafe@2.0.5:
    undefsafe: private
  undici-types@6.21.0:
    undici-types: private
ignoredBuilds:
  - esbuild
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.2.1
pendingBuilds: []
prunedAt: Fri, 30 May 2025 16:48:19 GMT
publicHoistPattern: []
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/linux-x64@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.25.5'
  - '@esbuild/win32-x64@0.25.5'
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
