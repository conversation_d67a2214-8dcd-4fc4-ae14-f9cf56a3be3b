#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Projects/srvai-mcp/node_modules/.pnpm/create-jest@29.7.0_@types+node@22.15.28/node_modules/create-jest/bin/node_modules:/Users/<USER>/Projects/srvai-mcp/node_modules/.pnpm/create-jest@29.7.0_@types+node@22.15.28/node_modules/create-jest/node_modules:/Users/<USER>/Projects/srvai-mcp/node_modules/.pnpm/create-jest@29.7.0_@types+node@22.15.28/node_modules:/Users/<USER>/Projects/srvai-mcp/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Projects/srvai-mcp/node_modules/.pnpm/create-jest@29.7.0_@types+node@22.15.28/node_modules/create-jest/bin/node_modules:/Users/<USER>/Projects/srvai-mcp/node_modules/.pnpm/create-jest@29.7.0_@types+node@22.15.28/node_modules/create-jest/node_modules:/Users/<USER>/Projects/srvai-mcp/node_modules/.pnpm/create-jest@29.7.0_@types+node@22.15.28/node_modules:/Users/<USER>/Projects/srvai-mcp/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../create-jest/bin/create-jest.js" "$@"
else
  exec node  "$basedir/../create-jest/bin/create-jest.js" "$@"
fi
