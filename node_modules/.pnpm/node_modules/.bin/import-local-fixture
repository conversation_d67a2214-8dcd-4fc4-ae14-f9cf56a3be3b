#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Projects/srvai-mcp/node_modules/.pnpm/import-local@3.2.0/node_modules/import-local/fixtures/node_modules:/Users/<USER>/Projects/srvai-mcp/node_modules/.pnpm/import-local@3.2.0/node_modules/import-local/node_modules:/Users/<USER>/Projects/srvai-mcp/node_modules/.pnpm/import-local@3.2.0/node_modules:/Users/<USER>/Projects/srvai-mcp/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Projects/srvai-mcp/node_modules/.pnpm/import-local@3.2.0/node_modules/import-local/fixtures/node_modules:/Users/<USER>/Projects/srvai-mcp/node_modules/.pnpm/import-local@3.2.0/node_modules/import-local/node_modules:/Users/<USER>/Projects/srvai-mcp/node_modules/.pnpm/import-local@3.2.0/node_modules:/Users/<USER>/Projects/srvai-mcp/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../import-local/fixtures/cli.js" "$@"
else
  exec node  "$basedir/../import-local/fixtures/cli.js" "$@"
fi
