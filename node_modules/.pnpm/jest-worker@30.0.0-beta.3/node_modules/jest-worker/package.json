{"name": "jest-worker", "version": "30.0.0-beta.3", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-worker"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@types/node": "*", "@ungap/structured-clone": "^1.2.0", "jest-util": "30.0.0-beta.3", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "devDependencies": {"@babel/core": "^7.11.6", "@types/merge-stream": "^2.0.0", "@types/supports-color": "^8.1.0", "@types/ungap__structured-clone": "^1.2.0", "get-stream": "^6.0.0", "jest-leak-detector": "30.0.0-beta.3", "worker-farm": "^1.6.0"}, "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "a123a3b667a178fb988662aaa1bc6308af759017"}