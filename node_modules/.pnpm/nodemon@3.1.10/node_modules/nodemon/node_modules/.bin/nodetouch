#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Projects/srvai-mcp/node_modules/.pnpm/touch@3.1.1/node_modules/touch/bin/node_modules:/Users/<USER>/Projects/srvai-mcp/node_modules/.pnpm/touch@3.1.1/node_modules/touch/node_modules:/Users/<USER>/Projects/srvai-mcp/node_modules/.pnpm/touch@3.1.1/node_modules:/Users/<USER>/Projects/srvai-mcp/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Projects/srvai-mcp/node_modules/.pnpm/touch@3.1.1/node_modules/touch/bin/node_modules:/Users/<USER>/Projects/srvai-mcp/node_modules/.pnpm/touch@3.1.1/node_modules/touch/node_modules:/Users/<USER>/Projects/srvai-mcp/node_modules/.pnpm/touch@3.1.1/node_modules:/Users/<USER>/Projects/srvai-mcp/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../touch@3.1.1/node_modules/touch/bin/nodetouch.js" "$@"
else
  exec node  "$basedir/../../../../../touch@3.1.1/node_modules/touch/bin/nodetouch.js" "$@"
fi
