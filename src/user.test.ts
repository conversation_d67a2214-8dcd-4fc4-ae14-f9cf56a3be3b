import { describe, it, expect, beforeEach } from '@jest/globals';
import { UserService, type User, type CreateUserInput } from './user.js';

describe('UserService', () => {
  let userService: UserService;

  beforeEach(() => {
    userService = new UserService();
  });

  describe('addUser', () => {
    it('should add a user and assign an ID', () => {
      const userData: CreateUserInput = {
        name: '<PERSON>',
        email: '<EMAIL>',
      };

      const user = userService.addUser(userData);

      expect(user).toEqual({
        id: 1,
        name: '<PERSON>',
        email: '<EMAIL>',
      });
    });

    it('should assign incremental IDs to multiple users', () => {
      const user1 = userService.addUser({
        name: 'Alice',
        email: '<EMAIL>',
      });

      const user2 = userService.addUser({
        name: '<PERSON>',
        email: '<EMAIL>',
      });

      expect(user1.id).toBe(1);
      expect(user2.id).toBe(2);
    });
  });

  describe('getUsers', () => {
    it('should return an empty array when no users exist', () => {
      const users = userService.getUsers();
      expect(users).toEqual([]);
    });

    it('should return all users', () => {
      userService.addUser({ name: 'Alice', email: '<EMAIL>' });
      userService.addUser({ name: 'Bob', email: '<EMAIL>' });

      const users = userService.getUsers();

      expect(users).toHaveLength(2);
      expect(users[0].name).toBe('Alice');
      expect(users[1].name).toBe('Bob');
    });

    it('should return a copy of the users array', () => {
      userService.addUser({ name: 'Alice', email: '<EMAIL>' });
      
      const users1 = userService.getUsers();
      const users2 = userService.getUsers();

      expect(users1).not.toBe(users2); // Different array instances
      expect(users1).toEqual(users2); // Same content
    });
  });

  describe('getUserById', () => {
    it('should return undefined when user does not exist', () => {
      const user = userService.getUserById(999);
      expect(user).toBeUndefined();
    });

    it('should return the correct user when found', () => {
      const addedUser = userService.addUser({
        name: 'Alice',
        email: '<EMAIL>',
      });

      const foundUser = userService.getUserById(addedUser.id);

      expect(foundUser).toEqual(addedUser);
    });
  });

  describe('updateUser', () => {
    it('should return undefined when user does not exist', () => {
      const result = userService.updateUser(999, { name: 'New Name' });
      expect(result).toBeUndefined();
    });

    it('should update user name', () => {
      const user = userService.addUser({
        name: 'Alice',
        email: '<EMAIL>',
      });

      const updatedUser = userService.updateUser(user.id, {
        name: 'Alice Smith',
      });

      expect(updatedUser).toEqual({
        id: user.id,
        name: 'Alice Smith',
        email: '<EMAIL>',
      });
    });

    it('should update user email', () => {
      const user = userService.addUser({
        name: 'Bob',
        email: '<EMAIL>',
      });

      const updatedUser = userService.updateUser(user.id, {
        email: '<EMAIL>',
      });

      expect(updatedUser?.email).toBe('<EMAIL>');
      expect(updatedUser?.name).toBe('Bob'); // Should remain unchanged
    });
  });

  describe('deleteUser', () => {
    it('should return false when user does not exist', () => {
      const result = userService.deleteUser(999);
      expect(result).toBe(false);
    });

    it('should delete user and return true', () => {
      const user = userService.addUser({
        name: 'Alice',
        email: '<EMAIL>',
      });

      const result = userService.deleteUser(user.id);

      expect(result).toBe(true);
      expect(userService.getUserById(user.id)).toBeUndefined();
      expect(userService.getUserCount()).toBe(0);
    });
  });

  describe('getUserCount', () => {
    it('should return 0 when no users exist', () => {
      expect(userService.getUserCount()).toBe(0);
    });

    it('should return correct count after adding users', () => {
      userService.addUser({ name: 'Alice', email: '<EMAIL>' });
      expect(userService.getUserCount()).toBe(1);

      userService.addUser({ name: 'Bob', email: '<EMAIL>' });
      expect(userService.getUserCount()).toBe(2);
    });

    it('should return correct count after deleting users', () => {
      const user1 = userService.addUser({ name: 'Alice', email: '<EMAIL>' });
      const user2 = userService.addUser({ name: 'Bob', email: '<EMAIL>' });

      expect(userService.getUserCount()).toBe(2);

      userService.deleteUser(user1.id);
      expect(userService.getUserCount()).toBe(1);

      userService.deleteUser(user2.id);
      expect(userService.getUserCount()).toBe(0);
    });
  });

  describe('clearUsers', () => {
    it('should remove all users', () => {
      userService.addUser({ name: 'Alice', email: '<EMAIL>' });
      userService.addUser({ name: 'Bob', email: '<EMAIL>' });

      expect(userService.getUserCount()).toBe(2);

      userService.clearUsers();

      expect(userService.getUserCount()).toBe(0);
      expect(userService.getUsers()).toEqual([]);
    });
  });

  describe('getUsersByEmailDomain', () => {
    beforeEach(() => {
      userService.addUser({ name: 'Alice', email: '<EMAIL>' });
      userService.addUser({ name: 'Bob', email: '<EMAIL>' });
      userService.addUser({ name: 'Charlie', email: '<EMAIL>' });
      userService.addUser({ name: 'David', email: '<EMAIL>' });
    });

    it('should return users with matching email domain', () => {
      const exampleUsers = userService.getUsersByEmailDomain('example.com');

      expect(exampleUsers).toHaveLength(3);
      expect(exampleUsers.map(u => u.name)).toEqual(['Alice', 'Bob', 'David']);
    });

    it('should be case insensitive', () => {
      const exampleUsers = userService.getUsersByEmailDomain('EXAMPLE.COM');

      expect(exampleUsers).toHaveLength(3);
    });

    it('should return empty array when no matches found', () => {
      const users = userService.getUsersByEmailDomain('nonexistent.com');

      expect(users).toEqual([]);
    });

    it('should handle different domains', () => {
      const testUsers = userService.getUsersByEmailDomain('test.org');

      expect(testUsers).toHaveLength(1);
      expect(testUsers[0].name).toBe('Charlie');
    });
  });
});
