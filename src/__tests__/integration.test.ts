import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';

describe('Application Integration Tests', () => {
  let consoleSpy: jest.SpiedFunction<typeof console.log>;

  beforeEach(() => {
    // Spy on console.log to capture output
    consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    // Restore console.log
    consoleSpy.mockRestore();
  });

  it('should run the main application without errors', async () => {
    // Import the main module to trigger execution
    // Note: This is a bit tricky with ES modules, so we'll test the UserService directly
    const { UserService } = await import('../user.js');
    
    const userService = new UserService();
    
    // Simulate the main application flow
    userService.addUser({
      name: '<PERSON>',
      email: '<EMAIL>',
    });
    
    userService.addUser({
      name: '<PERSON>',
      email: '<EMAIL>',
    });
    
    const users = userService.getUsers();
    expect(users).toHaveLength(2);
    
    const foundUser = userService.getUserById(1);
    expect(foundUser).toBeDefined();
    expect(foundUser?.name).toBe('Alice <PERSON>');
  });

  it('should handle Node.js environment variables', () => {
    expect(process.version).toBeDefined();
    expect(process.platform).toBeDefined();
    expect(process.version).toMatch(/^v\d+\.\d+\.\d+/);
  });

  it('should have correct Node.js version', () => {
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0], 10);
    
    // Should be Node.js 22 or later
    expect(majorVersion).toBeGreaterThanOrEqual(22);
  });
});
