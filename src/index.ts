#!/usr/bin/env node

/**
 * Main entry point for the TypeScript Node.js application
 */

import { UserService } from './user.js';

async function main(): Promise<void> {
  console.log('🚀 TypeScript Node.js project is running!');
  console.log(`Node.js version: ${process.version}`);
  console.log(`Platform: ${process.platform}`);

  const userService = new UserService();

  // Add some sample users
  userService.addUser({
    name: '<PERSON>',
    email: '<EMAIL>',
  });

  userService.addUser({
    name: '<PERSON>',
    email: '<EMAIL>',
  });

  console.log('\n📋 Users:');
  userService.getUsers().forEach(user => {
    console.log(`  ${user.id}: ${user.name} (${user.email})`);
  });

  console.log('\n🔍 Finding user with ID 1:');
  const foundUser = userService.getUserById(1);
  if (foundUser) {
    console.log(`  Found: ${foundUser.name}`);
  } else {
    console.log('  User not found');
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Run the main function
main().catch((error) => {
  console.error('Error in main:', error);
  process.exit(1);
});
