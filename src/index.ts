#!/usr/bin/env node

/**
 * Main entry point for the TypeScript Node.js application
 */

interface User {
  id: number;
  name: string;
  email: string;
}

class UserService {
  private users: User[] = [];

  addUser(user: Omit<User, 'id'>): User {
    const newUser: User = {
      id: this.users.length + 1,
      ...user,
    };
    this.users.push(newUser);
    return newUser;
  }

  getUsers(): User[] {
    return [...this.users];
  }

  getUserById(id: number): User | undefined {
    return this.users.find(user => user.id === id);
  }
}

async function main(): Promise<void> {
  console.log('🚀 TypeScript Node.js project is running!');
  console.log(`Node.js version: ${process.version}`);
  console.log(`Platform: ${process.platform}`);
  
  const userService = new UserService();
  
  // Add some sample users
  const user1 = userService.addUser({
    name: '<PERSON>',
    email: '<EMAIL>',
  });
  
  const user2 = userService.addUser({
    name: '<PERSON>',
    email: '<EMAIL>',
  });
  
  console.log('\n📋 Users:');
  userService.getUsers().forEach(user => {
    console.log(`  ${user.id}: ${user.name} (${user.email})`);
  });
  
  console.log('\n🔍 Finding user with ID 1:');
  const foundUser = userService.getUserById(1);
  if (foundUser) {
    console.log(`  Found: ${foundUser.name}`);
  } else {
    console.log('  User not found');
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Run the main function
main().catch((error) => {
  console.error('Error in main:', error);
  process.exit(1);
});
