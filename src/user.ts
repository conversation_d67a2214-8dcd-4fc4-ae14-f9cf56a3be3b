/**
 * User-related types and services
 */

export interface User {
  id: number;
  name: string;
  email: string;
}

export type CreateUserInput = Omit<User, 'id'>;

export class UserService {
  private users: User[] = [];

  /**
   * Add a new user to the service
   * @param user User data without ID
   * @returns The created user with assigned ID
   */
  addUser(user: CreateUserInput): User {
    const newUser: User = {
      id: this.users.length + 1,
      ...user,
    };
    this.users.push(newUser);
    return newUser;
  }

  /**
   * Get all users
   * @returns Array of all users
   */
  getUsers(): User[] {
    return [...this.users];
  }

  /**
   * Find a user by ID
   * @param id User ID to search for
   * @returns User if found, undefined otherwise
   */
  getUserById(id: number): User | undefined {
    return this.users.find(user => user.id === id);
  }

  /**
   * Update a user by ID
   * @param id User ID to update
   * @param updates Partial user data to update
   * @returns Updated user if found, undefined otherwise
   */
  updateUser(id: number, updates: Partial<CreateUserInput>): User | undefined {
    const userIndex = this.users.findIndex(user => user.id === id);
    if (userIndex === -1) {
      return undefined;
    }

    this.users[userIndex] = {
      ...this.users[userIndex],
      ...updates,
    };

    return this.users[userIndex];
  }

  /**
   * Delete a user by ID
   * @param id User ID to delete
   * @returns True if user was deleted, false if not found
   */
  deleteUser(id: number): boolean {
    const userIndex = this.users.findIndex(user => user.id === id);
    if (userIndex === -1) {
      return false;
    }

    this.users.splice(userIndex, 1);
    return true;
  }

  /**
   * Get the total number of users
   * @returns Number of users
   */
  getUserCount(): number {
    return this.users.length;
  }

  /**
   * Clear all users
   */
  clearUsers(): void {
    this.users = [];
  }

  /**
   * Find users by email domain
   * @param domain Email domain to search for
   * @returns Array of users with matching email domain
   */
  getUsersByEmailDomain(domain: string): User[] {
    return this.users.filter(user => 
      user.email.toLowerCase().endsWith(`@${domain.toLowerCase()}`)
    );
  }
}
