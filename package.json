{"name": "srvai-mcp", "version": "1.0.0", "description": "TypeScript Node.js project", "main": "dist/index.js", "type": "module", "engines": {"node": ">=22.0.0"}, "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "keywords": ["typescript", "nodejs", "pnpm"], "author": "", "license": "ISC", "devDependencies": {"@jest/globals": "30.0.0-beta.3", "@types/jest": "^29.5.14", "@types/node": "^22.15.28", "jest": "^29.7.0", "nodemon": "^3.1.10", "ts-jest": "^29.3.4", "tsx": "^4.19.4", "typescript": "^5.8.3"}}