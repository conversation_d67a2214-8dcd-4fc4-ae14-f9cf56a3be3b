{"name": "srvai-mcp", "version": "1.0.0", "description": "TypeScript Node.js project", "main": "dist/index.js", "type": "module", "engines": {"node": ">=22.0.0"}, "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["typescript", "nodejs", "pnpm"], "author": "", "license": "ISC", "devDependencies": {"@types/node": "^22.15.28", "nodemon": "^3.1.10", "tsx": "^4.19.4", "typescript": "^5.8.3"}}