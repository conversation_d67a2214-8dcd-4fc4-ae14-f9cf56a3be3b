# TypeScript Node.js Project

A modern TypeScript Node.js project using pnpm for package management.

## Requirements

- Node.js >= 22.0.0
- pnpm

## Installation

```bash
pnpm install
```

## Scripts

- `pnpm dev` - Run the project in development mode with hot reload
- `pnpm build` - Build the TypeScript project to JavaScript
- `pnpm start` - Run the built JavaScript project
- `pnpm clean` - Clean the dist directory
- `pnpm type-check` - Type check without emitting files

## Development

To start development:

```bash
pnpm dev
```

This will run the TypeScript file directly with hot reload using `tsx`.

## Building

To build the project:

```bash
pnpm build
```

This will compile TypeScript to JavaScript in the `dist` directory.

## Running

To run the built project:

```bash
pnpm start
```

## Project Structure

```
├── src/
│   └── index.ts          # Main application entry point
├── dist/                 # Built JavaScript files (generated)
├── package.json          # Project configuration and dependencies
├── tsconfig.json         # TypeScript configuration
└── README.md            # This file
```

## Features

- ✅ TypeScript with strict configuration
- ✅ ES Modules support
- ✅ Modern Node.js (v22+) compatibility
- ✅ Development hot reload with tsx
- ✅ Source maps for debugging
- ✅ Type declarations generation
- ✅ pnpm for fast, efficient package management
